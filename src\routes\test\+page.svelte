<script>
	import { onMount } from 'svelte';
	import questionsData from '$lib/2023_Dec_Int-A_New.json';

	let currentQuestionIndex = 0;
	let currentQuestion = $derived(questionsData[currentQuestionIndex]);

	// Function to render math after content updates
	function renderMath() {
		try {
			if (typeof window !== 'undefined' && window.renderMathInElement) {
				window.renderMathInElement(document.body, {
					delimiters: [
						{ left: '$$', right: '$$', display: true },
						{ left: '$', right: '$', display: false }
					],
					throwOnError: false
				});
			}
		} catch (error) {
			console.error('Math rendering error:', error);
		}
	}

	// Re-render math when question changes
	$effect(() => {
		currentQuestion;
		setTimeout(renderMath, 100); // Small delay to ensure DOM is updated
	});

	onMount(() => {
		renderMath();
	});

	function nextQuestion() {
		if (currentQuestionIndex < questionsData.length - 1) {
			currentQuestionIndex++;
		}
	}

	function prevQuestion() {
		if (currentQuestionIndex > 0) {
			currentQuestionIndex--;
		}
	}

	function goToQuestion(index) {
		currentQuestionIndex = index;
	}
</script>

<svelte:head>
	<title>Test Questions - DSAT16</title>
	<link
		rel="stylesheet"
		href="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.css"
		integrity="sha384-wcIxkf4k558AjM3Yz3BBFQUbk/zgIYC2R0QpeeYb+TwlBVMrlgLqwRjRtGZiK7ww"
		crossorigin="anonymous"
	/>
	<script
		src="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.js"
		integrity="sha384-hIoBPJpTUs74ddyc4bFZSM1TVlQDA60VBbJS0oA934VSz82sBx1X7kSx2ATBDIyd"
		crossorigin="anonymous"
	></script>
	<script
		src="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/contrib/auto-render.min.js"
		integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk"
		crossorigin="anonymous"
	></script>
</svelte:head>

<div class="container">
	<div class="header">
		<h1>Test Questions</h1>
		<div class="navigation">
			<button on:click={prevQuestion} disabled={currentQuestionIndex === 0}>
				← Previous
			</button>
			<span class="question-counter">
				Question {currentQuestionIndex + 1} of {questionsData.length}
			</span>
			<button on:click={nextQuestion} disabled={currentQuestionIndex === questionsData.length - 1}>
				Next →
			</button>
		</div>
	</div>

	<div class="question-selector">
		{#each questionsData as _, index}
			<button 
				class="question-btn" 
				class:active={index === currentQuestionIndex}
				on:click={() => goToQuestion(index)}
			>
				{index + 1}
			</button>
		{/each}
	</div>

	<div class="question-content">
		<div class="question-header">
			<h2>Question {currentQuestion.question_number}</h2>
			<div class="question-meta">
				<span class="module">Module: {currentQuestion.module}</span>
				<span class="id">ID: {currentQuestion.id}</span>
			</div>
		</div>

		{#if currentQuestion.passage}
			<div class="passage">
				<h3>Passage</h3>
				<div class="content">{@html currentQuestion.passage}</div>
			</div>
		{/if}

		<div class="question">
			<h3>Question</h3>
			<div class="content">{@html currentQuestion.question}</div>
		</div>

		<div class="answer-choices">
			<h3>Answer Choices</h3>
			<div class="choices">
				{#each currentQuestion.answer_choices as choice, index}
					<div class="choice">
						<span class="choice-letter">{String.fromCharCode(65 + index)}.</span>
						<span class="choice-text">{@html choice}</span>
					</div>
				{/each}
			</div>
		</div>

		<div class="answer">
			<h3>Correct Answer</h3>
			<div class="content correct-answer">{currentQuestion.answer}</div>
		</div>

		<div class="explanation">
			<h3>Explanation</h3>
			<div class="content">{@html currentQuestion.explanation}</div>
		</div>
	</div>
</div>

<style>
	.container {
		max-width: 1000px;
		margin: 0 auto;
		padding: 2rem;
		font-family: system-ui, -apple-system, sans-serif;
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 2rem;
		padding-bottom: 1rem;
		border-bottom: 2px solid #e5e7eb;
	}

	.header h1 {
		margin: 0;
		color: #1f2937;
	}

	.navigation {
		display: flex;
		align-items: center;
		gap: 1rem;
	}

	.navigation button {
		padding: 0.5rem 1rem;
		background: #3b82f6;
		color: white;
		border: none;
		border-radius: 0.375rem;
		cursor: pointer;
		transition: background-color 0.2s;
	}

	.navigation button:hover:not(:disabled) {
		background: #2563eb;
	}

	.navigation button:disabled {
		background: #9ca3af;
		cursor: not-allowed;
	}

	.question-counter {
		font-weight: 500;
		color: #6b7280;
	}

	.question-selector {
		display: flex;
		flex-wrap: wrap;
		gap: 0.5rem;
		margin-bottom: 2rem;
		padding: 1rem;
		background: #f9fafb;
		border-radius: 0.5rem;
	}

	.question-btn {
		width: 2.5rem;
		height: 2.5rem;
		border: 1px solid #d1d5db;
		background: white;
		border-radius: 0.375rem;
		cursor: pointer;
		transition: all 0.2s;
	}

	.question-btn:hover {
		background: #e5e7eb;
	}

	.question-btn.active {
		background: #3b82f6;
		color: white;
		border-color: #3b82f6;
	}

	.question-content {
		background: white;
		border: 1px solid #e5e7eb;
		border-radius: 0.5rem;
		overflow: hidden;
	}

	.question-header {
		padding: 1.5rem;
		background: #f8fafc;
		border-bottom: 1px solid #e5e7eb;
	}

	.question-header h2 {
		margin: 0 0 0.5rem 0;
		color: #1f2937;
	}

	.question-meta {
		display: flex;
		gap: 1rem;
		font-size: 0.875rem;
		color: #6b7280;
	}

	.passage, .question, .answer-choices, .answer, .explanation {
		padding: 1.5rem;
		border-bottom: 1px solid #e5e7eb;
	}

	.explanation {
		border-bottom: none;
	}

	.passage h3, .question h3, .answer-choices h3, .answer h3, .explanation h3 {
		margin: 0 0 1rem 0;
		color: #374151;
		font-size: 1.125rem;
		font-weight: 600;
	}

	.content {
		line-height: 1.6;
		color: #374151;
	}

	.choices {
		display: flex;
		flex-direction: column;
		gap: 0.75rem;
	}

	.choice {
		display: flex;
		gap: 0.5rem;
		padding: 0.75rem;
		background: #f9fafb;
		border-radius: 0.375rem;
	}

	.choice-letter {
		font-weight: 600;
		color: #374151;
		min-width: 1.5rem;
	}

	.choice-text {
		flex: 1;
	}

	.correct-answer {
		font-weight: 600;
		color: #059669;
		background: #ecfdf5;
		padding: 0.75rem;
		border-radius: 0.375rem;
	}

	/* Math rendering styles */
	:global(.katex) {
		font-size: 1em;
	}

	:global(.katex-display) {
		margin: 1rem 0;
	}

	/* Responsive design */
	@media (max-width: 768px) {
		.container {
			padding: 1rem;
		}

		.header {
			flex-direction: column;
			gap: 1rem;
			align-items: stretch;
		}

		.navigation {
			justify-content: center;
		}

		.question-selector {
			justify-content: center;
		}
	}
</style>
