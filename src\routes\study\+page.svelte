<script lang="ts">
    import { user } from '$lib/firebase';
    import H2 from '$lib/ui/typography/H2.svelte';
    import P1 from '$lib/ui/typography/P1.svelte';
    import QuestionBankProgress from '$lib/dashboard/QuestionBankProgress.svelte';
    import EstimatedScore from '$lib/dashboard/EstimatedScore.svelte';
    import UniAim from '$lib/dashboard/UniAim.svelte';
    import VocabProgress from '$lib/dashboard/VocabProgress.svelte';
    import DailyMissions from '$lib/dashboard/DailyMissions.svelte';
    import StreakBadge from '$lib/dashboard/StreakBadge.svelte';
</script>

<svelte:head>
    <title>Study - DSAT16</title>
</svelte:head>

<div class="dashboard-container">
    <H2>Welcome{$user ? `, ${$user.displayName}` : ""}!</H2>
    <section class="cards-container flex flex-row gap-4 w-full">
        <div class="flex-grow-[2] flex flex-col gap-4 h-full w-full">
            <div class="card flex-grow-[1]" style="background: var(--very-light-sky-blue, #f5fdff);">
                <QuestionBankProgress />
            </div>
            <div class="flex-grow-[1] flex flex-row gap-4">
                <div class="card flex-grow-[2]" style="background: var(--light-yellow, #FFF1C1);">
                    <DailyMissions />
                </div>
                <div class="card flex-grow-[1] max-w-[300px]" style="background: var(--light-tangerine, #FDE2C5);">
                    <StreakBadge />
                </div>
            </div>
            <!-- <div class="flex-grow-[1] flex flex-row gap-4">
                <div class="card flex-grow-[1] max-w-[400px]" style="background: var(--light-yellow, #FFF1C1);">
                    <EstimatedScore />
                </div>
                <div class="card flex-grow-[3] h-full" style="background: var(--light-tangerine, #FDE2C5);">
                    <VocabProgress />
                </div>
            </div> -->
        </div>
        <!-- <div class="flex-grow-[1] max-w-[400px]">
            <div class="card" style="background: var(--light-purple, #EEE5FF);">
                <UniAim />
            </div>
        </div> -->
    </section>
</div>



<style>
    .dashboard-container {
        display: flex;
        gap: 1.25rem;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        padding: 4rem;
    }
    
    .cards-container {
        max-width: 80rem;
    }

    .card {
        border-radius: 0.75rem;
        border: 0.125rem solid var(--pitch-black);
        box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
        height: 100%;
        width: 100%;
        padding: 1.25rem;
    }

    @media (max-width: 768px) {
        .dashboard-container {
            padding: 1rem;
        }

        .cards-container {
            flex-direction: column;
        }

        .card {
            max-width: 100%;
        }
    }
</style>