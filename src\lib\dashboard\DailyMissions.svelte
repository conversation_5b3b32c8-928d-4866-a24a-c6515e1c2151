<script lang="ts">
	import { missionProgress, missionCompletionPercentages, missionLoading, missionError, allMissionsComplete } from '../stores/mission.store.js';
	import { getDailyMissions } from '../missions/missionCatalog.js';
	import ProgressBar from '../analysis/ProgressBar.svelte';
	import H2 from '../ui/typography/H2.svelte';

	const dailyMissions = getDailyMissions();

	function getMissionProgress(missionId: string): { current: number; target: number; percentage: number } {
		const mission = dailyMissions.find(m => m.id === missionId);
		if (!mission) return { current: 0, target: 0, percentage: 0 };

		const current = $missionProgress?.missions[missionId] || 0;
		const target = mission.target;
		const percentage = $missionCompletionPercentages[missionId] || 0;

		return { current, target, percentage };
	}
</script>

<div class="bg-white rounded-lg shadow-md p-6">
	<div class="flex items-center justify-between mb-4">
		<H2>Daily Missions</H2>
		{#if $allMissionsComplete}
			<div class="flex items-center text-green-600">
				<svg class="w-5 h-5 mr-1" fill="currentColor" viewBox="0 0 20 20">
					<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
				</svg>
				<span class="text-sm font-medium">All Complete!</span>
			</div>
		{/if}
	</div>

	{#if $missionLoading}
		<div class="flex items-center justify-center py-8">
			<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
		</div>
	{:else if $missionError}
		<div class="text-red-600 text-center py-4">
			<p>{$missionError}</p>
		</div>
	{:else if $missionProgress}
		<div class="space-y-4">
			{#each dailyMissions as mission}
				{@const progress = getMissionProgress(mission.id)}
				{@const isComplete = progress.current >= progress.target}
				
				<div class="border rounded-lg p-4 {isComplete ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200'}">
					<div class="flex items-center justify-between mb-2">
						<div class="flex items-center">
							<h3 class="font-medium text-gray-900">{mission.name}</h3>
							{#if isComplete}
								<svg class="w-4 h-4 ml-2 text-green-600" fill="currentColor" viewBox="0 0 20 20">
									<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
								</svg>
							{/if}
						</div>
						<span class="text-sm font-medium {isComplete ? 'text-green-600' : 'text-gray-600'}">
							{progress.current}/{progress.target}
						</span>
					</div>
					
					<p class="text-sm text-gray-600 mb-3">{mission.description}</p>
					
					<ProgressBar 
						progress={progress.percentage} 
						color={isComplete ? 'green' : 'blue'}
						height="h-2"
					/>
					
					{#if mission.reward && isComplete}
						<div class="mt-2 text-xs text-green-600 font-medium">
							+{mission.reward} points earned!
						</div>
					{/if}
				</div>
			{/each}
		</div>

		{#if $allMissionsComplete}
			<div class="mt-6 p-4 bg-green-100 border border-green-200 rounded-lg text-center">
				<div class="text-green-800">
					<svg class="w-8 h-8 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
						<path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
					</svg>
					<h4 class="font-medium text-lg">Congratulations!</h4>
					<p class="text-sm mt-1">You've completed all daily missions. Keep up the great work!</p>
				</div>
			</div>
		{/if}
	{:else}
		<div class="text-center py-8 text-gray-500">
			<p>No mission data available</p>
		</div>
	{/if}
</div>
