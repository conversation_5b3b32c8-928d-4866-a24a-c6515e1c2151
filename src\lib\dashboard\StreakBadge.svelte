<script lang="ts">
	import { streakData } from '../stores/mission.store.js';
	import H4 from '../ui/typography/H4.svelte';

	function getStreakMessage(streak: number): string {
		if (streak === 0) return "Start your streak today!";
		if (streak === 1) return "Great start!";
		if (streak < 7) return "Building momentum!";
		if (streak < 30) return "On fire! 🔥";
		if (streak < 100) return "Incredible dedication!";
		return "Legendary streak! 🏆";
	}

	function getStreakColor(streak: number): string {
		if (streak === 0) return "text-gray-500";
		if (streak < 7) return "text-orange-500";
		if (streak < 30) return "text-red-500";
		return "text-purple-500";
	}

	function getStreakBgColor(streak: number): string {
		if (streak === 0) return "bg-gray-50 border-gray-200";
		if (streak < 7) return "bg-orange-50 border-orange-200";
		if (streak < 30) return "bg-red-50 border-red-200";
		return "bg-purple-50 border-purple-200";
	}

	function getMilestoneReward(streak: number): string | null {
		if (streak === 7) return "7-day milestone! 🎉";
		if (streak === 30) return "30-day milestone! 🏅";
		if (streak === 100) return "100-day milestone! 👑";
		if (streak > 0 && streak % 50 === 0) return `${streak}-day milestone! ⭐`;
		return null;
	}
</script>

<div class="bg-white rounded-lg shadow-md p-6">
	{#if $streakData}
		<div class="text-center">
			<div class="flex items-center justify-center mb-4">
				<div class="relative">
					<!-- Fire icon for active streaks -->
					{#if $streakData.currentStreak > 0}
						<svg class="w-12 h-12 {getStreakColor($streakData.currentStreak)}" fill="currentColor" viewBox="0 0 20 20">
							<path fill-rule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clip-rule="evenodd" />
						</svg>
					{:else}
						<!-- Calendar icon for zero streak -->
						<svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
						</svg>
					{/if}
				</div>
			</div>

			<H4>Current Streak</H4>
			
			<div class="mt-2 mb-4">
				<span class="text-4xl font-bold {getStreakColor($streakData.currentStreak)}">
					{$streakData.currentStreak}
				</span>
				<span class="text-lg text-gray-600 ml-1">
					{$streakData.currentStreak === 1 ? 'day' : 'days'}
				</span>
			</div>

			<p class="text-sm {getStreakColor($streakData.currentStreak)} font-medium mb-4">
				{getStreakMessage($streakData.currentStreak)}
			</p>

			<!-- Milestone celebration -->
			{#if getMilestoneReward($streakData.currentStreak)}
				<div class="mb-4 p-3 {getStreakBgColor($streakData.currentStreak)} border rounded-lg">
					<p class="text-sm font-medium {getStreakColor($streakData.currentStreak)}">
						{getMilestoneReward($streakData.currentStreak)}
					</p>
				</div>
			{/if}

			<!-- Best streak display -->
			{#if $streakData.bestStreak > 0}
				<div class="border-t pt-4 mt-4">
					<div class="flex items-center justify-center text-sm text-gray-600">
						<svg class="w-4 h-4 mr-1 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
							<path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
						</svg>
						<span>Best: {$streakData.bestStreak} {$streakData.bestStreak === 1 ? 'day' : 'days'}</span>
					</div>
				</div>
			{/if}

			<!-- Motivational tips -->
			{#if $streakData.currentStreak === 0}
				<div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
					<p class="text-xs text-blue-700">
						💡 Complete all daily missions to start your streak!
					</p>
				</div>
			{:else if $streakData.currentStreak < 7}
				<div class="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
					<p class="text-xs text-orange-700">
						🎯 Keep going! {7 - $streakData.currentStreak} more {7 - $streakData.currentStreak === 1 ? 'day' : 'days'} to reach your first week!
					</p>
				</div>
			{/if}
		</div>
	{:else}
		<div class="text-center py-4">
			<div class="animate-pulse">
				<div class="w-12 h-12 bg-gray-200 rounded-full mx-auto mb-4"></div>
				<div class="h-4 bg-gray-200 rounded w-24 mx-auto mb-2"></div>
				<div class="h-8 bg-gray-200 rounded w-16 mx-auto"></div>
			</div>
		</div>
	{/if}
</div>
