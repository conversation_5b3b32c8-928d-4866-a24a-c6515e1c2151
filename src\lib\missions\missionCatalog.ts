import type { Mission } from '../types/mission.types.js';
import { MissionMetric, MissionPeriod } from '../types/mission.types.js';

export const MISSION_CATALOG: Mission[] = [
	{
		id: 'daily_questions',
		name: 'Daily Questions',
		description: 'Answer 5 questions today',
		metric: MissionMetric.QUESTIONS_ANSWERED,
		target: 5,
		period: MissionPeriod.DAILY,
		reward: 100
	},
	{
		id: 'daily_vocab',
		name: 'Daily Vocab Session',
		description: 'Complete 1 vocabulary session today',
		metric: MissionMetric.VOCAB_SESSION,
		target: 1,
		period: MissionPeriod.DAILY,
		reward: 50
	}
] as const;

/**
 * Get a mission by its ID
 */
export function getMissionById(id: string): Mission | undefined {
	return MISSION_CATALOG.find(mission => mission.id === id);
}

/**
 * Get all daily missions
 */
export function getDailyMissions(): Mission[] {
	return MISSION_CATALOG.filter(mission => mission.period === MissionPeriod.DAILY);
}

/**
 * Get all missions for a specific metric
 */
export function getMissionsByMetric(metric: MissionMetric): Mission[] {
	return MISSION_CATALOG.filter(mission => mission.metric === metric);
}
